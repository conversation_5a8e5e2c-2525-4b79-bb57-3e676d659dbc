import { useState } from "react";
import { Header } from "@/components/layout/Header";
import { RiskCard } from "@/components/health/RiskCard";
import { MicroGoals } from "@/components/health/MicroGoals";
import { Gamification } from "@/components/health/Gamification";
import { VoiceAssistant } from "@/components/health/VoiceAssistant";
import { HealthTrends } from "@/components/health/HealthTrends";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Brain, 
  Mic, 
  TrendingUp, 
  AlertCircle, 
  Lightbulb, 
  Activity,
  FileText,
  Pill
} from "lucide-react";

interface PatientDashboardProps {
  user: any;
  onNavigate: (page: string) => void;
}

const riskData = [
  {
    condition: "Type 2 Diabetes",
    riskLevel: "medium" as const,
    percentage: 34,
    trend: "improving" as const,
    lastUpdated: "2 hours ago"
  },
  {
    condition: "Hypertension", 
    riskLevel: "low" as const,
    percentage: 18,
    trend: "stable" as const,
    lastUpdated: "1 hour ago"
  },
  {
    condition: "Cardiovascular Disease",
    riskLevel: "high" as const,
    percentage: 67,
    trend: "worsening" as const,
    lastUpdated: "30 minutes ago"
  }
];

const recommendations = [
  {
    id: '1',
    title: 'Increase Water Intake',
    description: 'Based on your recent glucose levels, drinking more water can help stabilize your blood sugar.',
    priority: 'high',
    category: 'Hydration',
    impact: 'May reduce glucose spikes by 15%'
  },
  {
    id: '2', 
    title: 'Evening Walk Routine',
    description: 'A 20-minute walk after dinner can improve insulin sensitivity and help with overnight glucose control.',
    priority: 'medium',
    category: 'Activity', 
    impact: 'Estimated 12% improvement in HbA1c'
  },
  {
    id: '3',
    title: 'Meal Timing Adjustment',
    description: 'Consider eating your largest meal earlier in the day to optimize your metabolic response.',
    priority: 'low',
    category: 'Nutrition',
    impact: 'May improve morning glucose by 8%'
  }
];

export function PatientDashboard({ user, onNavigate }: PatientDashboardProps) {
  const [activeAlert, setActiveAlert] = useState(true);

  return (
    <div className="min-h-screen bg-background">
      <Header 
        userType="patient" 
        userName={user.name} 
        onNavigate={onNavigate}
      />

      <main className="container mx-auto px-4 py-6 space-y-6">
        {/* Critical Alert */}
        {activeAlert && (
          <Alert className="border-health-danger/20 bg-risk-high animate-slide-up">
            <AlertCircle className="h-4 w-4 text-health-danger" />
            <AlertDescription className="flex items-center justify-between">
              <span>
                <strong>High glucose detected:</strong> 187 mg/dL at 2:30 PM. Consider checking ketones and taking corrective action.
              </span>
              <div className="flex gap-2">
                <Button size="sm" variant="outline" className="h-7">
                  Log Action
                </Button>
                <Button 
                  size="sm" 
                  variant="ghost" 
                  className="h-7"
                  onClick={() => setActiveAlert(false)}
                >
                  Dismiss
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Welcome Section */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-foreground">
              Good afternoon, {user.name.split(' ')[0]}! 👋
            </h1>
            <p className="text-muted-foreground">
              Here's your health summary for today
            </p>
          </div>
          
          <Button className="bg-gradient-health hover:bg-primary-hover flex items-center gap-2">
            <Mic className="h-4 w-4" />
            Voice Assistant
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Risk Cards & AI Insights */}
          <div className="lg:col-span-2 space-y-6">
            {/* Risk Assessment */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold flex items-center gap-2">
                  <Activity className="h-5 w-5 text-primary" />
                  Health Risk Assessment
                </h2>
                <Badge variant="secondary" className="text-xs">
                  AI-Powered Analysis
                </Badge>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {riskData.map((risk, index) => (
                  <RiskCard key={index} {...risk} />
                ))}
              </div>
            </div>

            {/* SHAP Explanations */}
            <Card className="shadow-card">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-semibold flex items-center gap-2">
                  <Brain className="h-5 w-5 text-primary" />
                  Risk Factors Explanation
                  <Badge variant="outline" className="text-xs">Explainable AI</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-risk-high rounded-lg">
                    <span className="font-medium text-sm">Recent glucose spikes</span>
                    <div className="flex items-center gap-2">
                      <div className="w-24 h-2 bg-health-danger/20 rounded-full overflow-hidden">
                        <div className="w-4/5 h-full bg-health-danger rounded-full"></div>
                      </div>
                      <span className="text-sm font-medium text-health-danger">+32%</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-risk-medium rounded-lg">
                    <span className="font-medium text-sm">Sleep quality decline</span>
                    <div className="flex items-center gap-2">
                      <div className="w-24 h-2 bg-health-warning/20 rounded-full overflow-hidden">
                        <div className="w-3/5 h-full bg-health-warning rounded-full"></div>
                      </div>
                      <span className="text-sm font-medium text-health-warning">+18%</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-risk-low rounded-lg">
                    <span className="font-medium text-sm">Medication adherence</span>
                    <div className="flex items-center gap-2">
                      <div className="w-24 h-2 bg-health-excellent/20 rounded-full overflow-hidden">
                        <div className="w-2/5 h-full bg-health-excellent rounded-full"></div>
                      </div>
                      <span className="text-sm font-medium text-health-excellent">-12%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Health Trends */}
            <HealthTrends />

            {/* AI Recommendations */}
            <Card className="shadow-card">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-semibold flex items-center gap-2">
                  <Lightbulb className="h-5 w-5 text-primary" />
                  Personalized Recommendations
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recommendations.map((rec) => (
                    <div key={rec.id} className="p-4 border rounded-lg hover:shadow-sm transition-all">
                      <div className="flex items-start justify-between mb-2">
                        <h4 className="font-medium text-sm">{rec.title}</h4>
                        <Badge 
                          variant={rec.priority === 'high' ? 'destructive' : rec.priority === 'medium' ? 'default' : 'secondary'}
                          className="text-xs"
                        >
                          {rec.priority} priority
                        </Badge>
                      </div>
                      
                      <p className="text-sm text-muted-foreground mb-3">{rec.description}</p>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">{rec.category}</Badge>
                          <span className="text-xs text-health-excellent">{rec.impact}</span>
                        </div>
                        <Button size="sm" variant="outline">Track Progress</Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Goals & Gamification */}
          <div className="space-y-6">
            <MicroGoals />
            <Gamification />
            
            {/* Quick Actions */}
            <Card className="shadow-card">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-semibold">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <FileText className="h-4 w-4 mr-2" />
                  Log Symptoms
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <Pill className="h-4 w-4 mr-2" />
                  Medication Reminder
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  View Trends
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
      
      <VoiceAssistant />
    </div>
  );
}