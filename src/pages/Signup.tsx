import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import { Heart, Lock, Mail, User, Building, Eye, EyeOff, Check, X } from "lucide-react";
import { toast } from "@/hooks/use-toast";

interface SignupProps {
  onSignup: (userType: 'patient' | 'clinician', userData: any) => void;
  onNavigateToLogin: () => void;
}

interface PasswordRequirement {
  text: string;
  met: boolean;
}

export function Signup({ onSignup, onNavigateToLogin }: SignupProps) {
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: ''
  });

  // Password validation requirements
  const passwordRequirements: PasswordRequirement[] = [
    { text: 'At least 8 characters long', met: formData.password.length >= 8 },
    { text: 'Contains uppercase letter', met: /[A-Z]/.test(formData.password) },
    { text: 'Contains lowercase letter', met: /[a-z]/.test(formData.password) },
    { text: 'Contains number', met: /\d/.test(formData.password) },
    { text: 'Passwords match', met: formData.password === formData.confirmPassword && formData.confirmPassword.length > 0 }
  ];

  const isValidPassword = passwordRequirements.slice(0, 4).every(req => req.met);
  const doPasswordsMatch = passwordRequirements[4].met;

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const validateEmail = (email: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  const handleSignup = async (userType: 'patient' | 'clinician', e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!formData.name.trim()) {
      toast({
        variant: "destructive",
        title: "Validation Error",
        description: "Please enter your full name."
      });
      return;
    }

    if (!validateEmail(formData.email)) {
      toast({
        variant: "destructive",
        title: "Validation Error",
        description: "Please enter a valid email address."
      });
      return;
    }

    if (!isValidPassword) {
      toast({
        variant: "destructive",
        title: "Password Error",
        description: "Please ensure your password meets all requirements."
      });
      return;
    }

    if (!doPasswordsMatch) {
      toast({
        variant: "destructive",
        title: "Password Error",
        description: "Passwords do not match."
      });
      return;
    }

    if (!agreedToTerms) {
      toast({
        variant: "destructive",
        title: "Terms Required",
        description: "Please agree to the Terms of Service and Privacy Policy."
      });
      return;
    }

    setLoading(true);
    
    // Simulate API call
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const userData = {
        name: formData.name,
        email: formData.email,
        userType,
        id: Math.random().toString(36).substr(2, 9),
        createdAt: new Date().toISOString()
      };

      toast({
        title: "Account Created!",
        description: `Welcome to HealthCoach AI, ${formData.name}! Please complete your profile.`,
      });

      onSignup(userType, userData);
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Signup Failed",
        description: "Something went wrong. Please try again."
      });
    } finally {
      setLoading(false);
    }
  };

  const RequirementCheck = ({ requirement }: { requirement: PasswordRequirement }) => (
    <div className={`flex items-center space-x-2 text-sm ${requirement.met ? 'text-health-excellent' : 'text-muted-foreground'}`}>
      {requirement.met ? <Check className="h-3 w-3" /> : <X className="h-3 w-3" />}
      <span>{requirement.text}</span>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-subtle flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Logo & Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center">
            <div className="h-12 w-12 rounded-xl bg-gradient-health flex items-center justify-center">
              <Heart className="h-6 w-6 text-white" />
            </div>
          </div>
          <div>
            <h1 className="text-3xl font-bold text-foreground">Join HealthCoach AI</h1>
            <p className="text-muted-foreground">Create your account to get started</p>
          </div>
        </div>

        <Card className="shadow-card">
          <CardHeader className="space-y-1 pb-4">
            <CardTitle className="text-2xl text-center">Create Account</CardTitle>
            <CardDescription className="text-center">
              Choose your account type to get personalized health insights
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <Tabs defaultValue="patient" className="space-y-4">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="patient" className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Patient
                </TabsTrigger>
                <TabsTrigger value="clinician" className="flex items-center gap-2">
                  <Building className="h-4 w-4" />
                  Clinician
                </TabsTrigger>
              </TabsList>

              <TabsContent value="patient" className="space-y-4">
                <form onSubmit={(e) => handleSignup('patient', e)}>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="patient-name">Full Name</Label>
                      <div className="relative">
                        <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="patient-name"
                          type="text"
                          placeholder="Enter your full name"
                          className="pl-10"
                          value={formData.name}
                          onChange={(e) => handleInputChange('name', e.target.value)}
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="patient-signup-email">Email Address</Label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="patient-signup-email"
                          type="email"
                          placeholder="Enter your email"
                          className="pl-10"
                          value={formData.email}
                          onChange={(e) => handleInputChange('email', e.target.value)}
                          required
                        />
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="patient-signup-password">Password</Label>
                      <div className="relative">
                        <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="patient-signup-password"
                          type={showPassword ? "text" : "password"}
                          placeholder="Create a strong password"
                          className="pl-10 pr-10"
                          value={formData.password}
                          onChange={(e) => handleInputChange('password', e.target.value)}
                          required
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="patient-confirm-password">Confirm Password</Label>
                      <div className="relative">
                        <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="patient-confirm-password"
                          type={showConfirmPassword ? "text" : "password"}
                          placeholder="Confirm your password"
                          className="pl-10 pr-10"
                          value={formData.confirmPassword}
                          onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                          required
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        >
                          {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                    </div>

                    {/* Password Requirements */}
                    {formData.password && (
                      <div className="space-y-2 p-3 bg-muted/30 rounded-lg">
                        <p className="text-sm font-medium">Password Requirements:</p>
                        {passwordRequirements.map((req, index) => (
                          <RequirementCheck key={index} requirement={req} />
                        ))}
                      </div>
                    )}

                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="patient-terms" 
                        checked={agreedToTerms}
                        onCheckedChange={(checked) => setAgreedToTerms(checked === true)}
                      />
                      <Label htmlFor="patient-terms" className="text-sm leading-tight">
                        I agree to the{" "}
                        <Button variant="link" className="text-xs px-0 h-auto">
                          Terms of Service
                        </Button>{" "}
                        and{" "}
                        <Button variant="link" className="text-xs px-0 h-auto">
                          Privacy Policy
                        </Button>
                      </Label>
                    </div>

                    <Button 
                      type="submit" 
                      className="w-full bg-gradient-health hover:bg-primary-hover"
                      disabled={loading || !isValidPassword || !doPasswordsMatch || !agreedToTerms}
                    >
                      {loading ? "Creating Account..." : "Create Patient Account"}
                    </Button>
                  </div>
                </form>
              </TabsContent>

              <TabsContent value="clinician" className="space-y-4">
                <form onSubmit={(e) => handleSignup('clinician', e)}>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="clinician-name">Full Name</Label>
                      <div className="relative">
                        <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="clinician-name"
                          type="text"
                          placeholder="Dr. John Smith"
                          className="pl-10"
                          value={formData.name}
                          onChange={(e) => handleInputChange('name', e.target.value)}
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="clinician-signup-email">Professional Email</Label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="clinician-signup-email"
                          type="email"
                          placeholder="<EMAIL>"
                          className="pl-10"
                          value={formData.email}
                          onChange={(e) => handleInputChange('email', e.target.value)}
                          required
                        />
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="clinician-signup-password">Password</Label>
                      <div className="relative">
                        <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="clinician-signup-password"
                          type={showPassword ? "text" : "password"}
                          placeholder="Create a strong password"
                          className="pl-10 pr-10"
                          value={formData.password}
                          onChange={(e) => handleInputChange('password', e.target.value)}
                          required
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="clinician-confirm-password">Confirm Password</Label>
                      <div className="relative">
                        <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="clinician-confirm-password"
                          type={showConfirmPassword ? "text" : "password"}
                          placeholder="Confirm your password"
                          className="pl-10 pr-10"
                          value={formData.confirmPassword}
                          onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                          required
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        >
                          {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                    </div>

                    {/* Password Requirements */}
                    {formData.password && (
                      <div className="space-y-2 p-3 bg-muted/30 rounded-lg">
                        <p className="text-sm font-medium">Password Requirements:</p>
                        {passwordRequirements.map((req, index) => (
                          <RequirementCheck key={index} requirement={req} />
                        ))}
                      </div>
                    )}

                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="clinician-terms" 
                        checked={agreedToTerms}
                        onCheckedChange={(checked) => setAgreedToTerms(checked === true)}
                      />
                      <Label htmlFor="clinician-terms" className="text-sm leading-tight">
                        I agree to the{" "}
                        <Button variant="link" className="text-xs px-0 h-auto">
                          Terms of Service
                        </Button>{" "}
                        and{" "}
                        <Button variant="link" className="text-xs px-0 h-auto">
                          Privacy Policy
                        </Button>
                      </Label>
                    </div>

                    <Button 
                      type="submit" 
                      className="w-full bg-gradient-health hover:bg-primary-hover"
                      disabled={loading || !isValidPassword || !doPasswordsMatch || !agreedToTerms}
                    >
                      {loading ? "Creating Account..." : "Create Clinician Account"}
                    </Button>
                  </div>
                </form>
              </TabsContent>
            </Tabs>

            <div className="mt-6">
              <Separator className="my-4" />
              
              <div className="space-y-2">
                <Button variant="outline" className="w-full">
                  <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
                    <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                  </svg>
                  Continue with Google
                </Button>
                
                <p className="text-xs text-center text-muted-foreground">
                  Already have an account?{" "}
                  <Button variant="link" className="text-xs px-0" onClick={onNavigateToLogin}>
                    Sign in here
                  </Button>
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="text-center text-xs text-muted-foreground">
          <p>By creating an account, you agree to our Terms of Service and Privacy Policy</p>
        </div>
      </div>
    </div>
  );
}