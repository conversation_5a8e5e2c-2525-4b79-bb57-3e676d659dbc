import { useState } from "react";
import { Head<PERSON> } from "@/components/layout/Header";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Search, 
  Filter, 
  Download, 
  AlertTriangle, 
  TrendingUp, 
  Users, 
  FileText,
  Calendar,
  Activity,
  Brain
} from "lucide-react";

interface Patient {
  id: string;
  name: string;
  age: number;
  conditions: string[];
  riskLevel: 'low' | 'medium' | 'high';
  lastVisit: string;
  nextAppointment?: string;
  adherence: number;
  alerts: number;
}

const mockPatients: Patient[] = [
  {
    id: '1',
    name: '<PERSON>',
    age: 58,
    conditions: ['Type 2 Diabetes', 'Hypertension'],
    riskLevel: 'high',
    lastVisit: '2024-01-10',
    nextAppointment: '2024-01-15',
    adherence: 78,
    alerts: 3
  },
  {
    id: '2', 
    name: 'Robert Chen',
    age: 45,
    conditions: ['Hypertension'],
    riskLevel: 'medium',
    lastVisit: '2024-01-08',
    nextAppointment: '2024-01-20',
    adherence: 92,
    alerts: 1
  },
  {
    id: '3',
    name: 'Maria Garcia',
    age: 62,
    conditions: ['Type 2 Diabetes', 'Cardiovascular Disease'],
    riskLevel: 'low',
    lastVisit: '2024-01-12',
    adherence: 95,
    alerts: 0
  },
  {
    id: '4',
    name: 'James Wilson', 
    age: 54,
    conditions: ['Type 2 Diabetes'],
    riskLevel: 'high',
    lastVisit: '2024-01-09',
    nextAppointment: '2024-01-14',
    adherence: 65,
    alerts: 2
  }
];

interface ClinicianDashboardProps {
  user: any;
  onNavigate: (page: string) => void;
}

export function ClinicianDashboard({ user, onNavigate }: ClinicianDashboardProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedRiskFilter, setSelectedRiskFilter] = useState<string>("all");

  const filteredPatients = mockPatients.filter(patient => {
    const matchesSearch = patient.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      patient.conditions.some(condition => condition.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesRisk = selectedRiskFilter === "all" || patient.riskLevel === selectedRiskFilter;
    
    return matchesSearch && matchesRisk;
  });

  const highRiskPatients = mockPatients.filter(p => p.riskLevel === 'high').length;
  const totalAlerts = mockPatients.reduce((sum, p) => sum + p.alerts, 0);
  const avgAdherence = Math.round(mockPatients.reduce((sum, p) => sum + p.adherence, 0) / mockPatients.length);

  return (
    <div className="min-h-screen bg-background">
      <Header 
        userType="clinician" 
        userName={user.name} 
        onNavigate={onNavigate}
      />

      <main className="container mx-auto px-4 py-6 space-y-6">
        {/* Welcome & Critical Alerts */}
        <div className="space-y-4">
          <div>
            <h1 className="text-2xl font-bold text-foreground">
              Welcome back, {user.name.split(' ')[0]}! 👨‍⚕️
            </h1>
            <p className="text-muted-foreground">
              Manage your patients and monitor their health progress
            </p>
          </div>

          {totalAlerts > 0 && (
            <Alert className="border-health-danger/20 bg-risk-high">
              <AlertTriangle className="h-4 w-4 text-health-danger" />
              <AlertDescription>
                <strong>{totalAlerts} active alerts</strong> across your patient panel require attention.
                <Button size="sm" variant="outline" className="ml-2 h-7">
                  Review All
                </Button>
              </AlertDescription>
            </Alert>
          )}
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="shadow-card">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Patients</p>
                  <p className="text-2xl font-bold">{mockPatients.length}</p>
                </div>
                <Users className="h-8 w-8 text-primary opacity-60" />
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-card">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">High Risk</p>
                  <p className="text-2xl font-bold text-health-danger">{highRiskPatients}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-health-danger opacity-60" />
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-card">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Active Alerts</p>
                  <p className="text-2xl font-bold text-health-warning">{totalAlerts}</p>
                </div>
                <Activity className="h-8 w-8 text-health-warning opacity-60" />
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-card">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Avg Adherence</p>
                  <p className="text-2xl font-bold text-health-excellent">{avgAdherence}%</p>
                </div>
                <TrendingUp className="h-8 w-8 text-health-excellent opacity-60" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Patient List */}
          <div className="lg:col-span-2">
            <Card className="shadow-card">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-semibold">Patient Management</CardTitle>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline">
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                    <Button size="sm" variant="outline">
                      <Filter className="h-4 w-4 mr-2" />
                      Filter
                    </Button>
                  </div>
                </div>

                {/* Search & Filters */}
                <div className="flex gap-4">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search patients by name or condition..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  
                  <div className="flex gap-2">
                    {['all', 'high', 'medium', 'low'].map((risk) => (
                      <Button
                        key={risk}
                        size="sm"
                        variant={selectedRiskFilter === risk ? "default" : "outline"}
                        onClick={() => setSelectedRiskFilter(risk)}
                        className="capitalize"
                      >
                        {risk} {risk !== 'all' && 'Risk'}
                      </Button>
                    ))}
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <div className="space-y-3">
                  {filteredPatients.map((patient) => (
                    <div 
                      key={patient.id}
                      className="p-4 border rounded-lg hover:shadow-sm transition-all cursor-pointer"
                      onClick={() => onNavigate('patient-detail')}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h4 className="font-medium">{patient.name}</h4>
                            <Badge 
                              variant={
                                patient.riskLevel === 'high' ? 'destructive' : 
                                patient.riskLevel === 'medium' ? 'default' : 'secondary'
                              }
                              className="text-xs"
                            >
                              {patient.riskLevel} risk
                            </Badge>
                            {patient.alerts > 0 && (
                              <Badge variant="outline" className="text-xs text-health-danger">
                                {patient.alerts} alert{patient.alerts > 1 ? 's' : ''}
                              </Badge>
                            )}
                          </div>
                          
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <span>Age: {patient.age}</span>
                            <span>•</span>
                            <span>{patient.conditions.join(', ')}</span>
                            <span>•</span>
                            <span>Adherence: {patient.adherence}%</span>
                          </div>
                          
                          <div className="flex items-center gap-4 text-xs text-muted-foreground mt-1">
                            <span>Last visit: {new Date(patient.lastVisit).toLocaleDateString()}</span>
                            {patient.nextAppointment && (
                              <>
                                <span>•</span>
                                <span>Next: {new Date(patient.nextAppointment).toLocaleDateString()}</span>
                              </>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Button size="sm" variant="outline">
                            <FileText className="h-4 w-4 mr-1" />
                            Report
                          </Button>
                          <Button size="sm" variant="outline">
                            <Calendar className="h-4 w-4 mr-1" />
                            Schedule
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Analytics & Tools */}
          <div className="space-y-6">
            {/* AI Insights */}
            <Card className="shadow-card">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-semibold flex items-center gap-2">
                  <Brain className="h-5 w-5 text-primary" />
                  AI Clinical Insights
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-3 bg-risk-high rounded-lg">
                  <p className="text-sm font-medium mb-1">Population Risk Trend</p>
                  <p className="text-xs text-muted-foreground">
                    23% increase in diabetes complications risk across your panel this month
                  </p>
                </div>
                
                <div className="p-3 bg-risk-medium rounded-lg">
                  <p className="text-sm font-medium mb-1">Medication Adherence</p>
                  <p className="text-xs text-muted-foreground">
                    3 patients showing declining adherence patterns
                  </p>
                </div>
                
                <div className="p-3 bg-risk-low rounded-lg">
                  <p className="text-sm font-medium mb-1">Intervention Success</p>
                  <p className="text-xs text-muted-foreground">
                    Recent lifestyle recommendations showing 18% improvement rate
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="shadow-card">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-semibold">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <FileText className="h-4 w-4 mr-2" />
                  Generate Report
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <Calendar className="h-4 w-4 mr-2" />
                  Schedule Follow-up
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Population Analytics
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <Users className="h-4 w-4 mr-2" />
                  Care Team Coordination
                </Button>
              </CardContent>
            </Card>

            {/* Upcoming Appointments */}
            <Card className="shadow-card">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-semibold">Today's Schedule</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {mockPatients
                  .filter(p => p.nextAppointment === '2024-01-15')
                  .map(patient => (
                    <div key={patient.id} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-sm">{patient.name}</p>
                          <p className="text-xs text-muted-foreground">
                            Follow-up • {patient.conditions[0]}
                          </p>
                        </div>
                        <span className="text-xs text-muted-foreground">2:30 PM</span>
                      </div>
                    </div>
                  ))}
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}