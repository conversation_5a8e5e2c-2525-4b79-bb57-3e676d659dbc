import React, { useState } from 'react';
import { User, Calendar, Heart, Pill, AlertTriangle, Camera, Save, Edit3, Plus, X } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/hooks/use-toast';

interface ProfileProps {
  user?: any;
  userType?: 'patient' | 'clinician' | null;
  onNavigateToDashboard?: () => void;
  onNavigate?: (page: string) => void;
}

const Profile = ({ user, userType, onNavigateToDashboard, onNavigate }: ProfileProps) => {
  const [editMode, setEditMode] = useState(false);
  const [profile, setProfile] = useState({
    name: '',
    age: '',
    gender: '',
    email: '',
    phone: '',
    bloodType: '',
    emergencyContact: '',
    conditions: [] as string[],
    medications: [] as { name: string; dosage: string; frequency: string }[],
    allergies: [] as string[],
    height: '',
    weight: '',
    notes: ''
  });

  // Sample data for auto-fill
  const sampleData = {
    name: 'Sarah Johnson',
    age: '45',
    gender: 'Female',
    email: '<EMAIL>',
    phone: '+****************',
    bloodType: 'O+',
    emergencyContact: 'John Johnson - +****************',
    conditions: ['Type 2 Diabetes', 'Hypertension', 'High Cholesterol'],
    medications: [
      { name: 'Metformin', dosage: '500mg', frequency: 'Twice daily' },
      { name: 'Lisinopril', dosage: '10mg', frequency: 'Once daily' },
      { name: 'Atorvastatin', dosage: '20mg', frequency: 'Once daily at bedtime' }
    ],
    allergies: ['Penicillin', 'Shellfish', 'Pollen'],
    height: '5\'6"',
    weight: '165 lbs',
    notes: 'Family history of diabetes. Prefer morning exercise. Vegetarian diet.'
  };

  const commonConditions = [
    'Type 1 Diabetes', 'Type 2 Diabetes', 'Hypertension', 'High Cholesterol',
    'Asthma', 'Heart Disease', 'Arthritis', 'Obesity', 'Depression', 'Anxiety'
  ];

  const commonAllergies = [
    'Penicillin', 'Aspirin', 'Shellfish', 'Nuts', 'Dairy', 'Pollen', 'Dust', 'Pet Dander'
  ];

  const handleAutoFill = (field: string) => {
    setProfile(prev => ({
      ...prev,
      [field]: sampleData[field as keyof typeof sampleData]
    }));
    toast({
      title: "Auto-filled!",
      description: `${field} has been filled with sample data.`,
    });
  };

  const handleAutoFillAll = () => {
    setProfile(sampleData);
    toast({
      title: "Profile Auto-Filled!",
      description: "All fields have been populated with sample data.",
    });
  };

  const addCondition = (condition: string) => {
    if (!profile.conditions.includes(condition)) {
      setProfile(prev => ({
        ...prev,
        conditions: [...prev.conditions, condition]
      }));
    }
  };

  const removeCondition = (condition: string) => {
    setProfile(prev => ({
      ...prev,
      conditions: prev.conditions.filter(c => c !== condition)
    }));
  };

  const addMedication = () => {
    setProfile(prev => ({
      ...prev,
      medications: [...prev.medications, { name: '', dosage: '', frequency: '' }]
    }));
  };

  const updateMedication = (index: number, field: string, value: string) => {
    setProfile(prev => ({
      ...prev,
      medications: prev.medications.map((med, i) => 
        i === index ? { ...med, [field]: value } : med
      )
    }));
  };

  const removeMedication = (index: number) => {
    setProfile(prev => ({
      ...prev,
      medications: prev.medications.filter((_, i) => i !== index)
    }));
  };

  const addAllergy = (allergy: string) => {
    if (!profile.allergies.includes(allergy)) {
      setProfile(prev => ({
        ...prev,
        allergies: [...prev.allergies, allergy]
      }));
    }
  };

  const removeAllergy = (allergy: string) => {
    setProfile(prev => ({
      ...prev,
      allergies: prev.allergies.filter(a => a !== allergy)
    }));
  };

  const handleSave = () => {
    setEditMode(false);
    toast({
      title: "Profile Saved!",
      description: "Your health profile has been updated successfully.",
    });
  };

  return (
    <div className="min-h-screen bg-gradient-subtle p-4 md:p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <User className="w-8 h-8 text-primary" />
            <div>
              <h1 className="text-3xl font-bold text-foreground">Health Profile</h1>
              {user && (
                <p className="text-muted-foreground">Welcome, {user.name}!</p>
              )}
            </div>
          </div>
          <div className="flex space-x-2">
            <Button
              onClick={handleAutoFillAll}
              variant="outline"
              className="bg-gradient-health text-white border-0 hover:opacity-90"
            >
              <Plus className="w-4 h-4 mr-2" />
              Auto-Fill Sample
            </Button>
            <Button
              onClick={() => setEditMode(!editMode)}
              variant={editMode ? "destructive" : "default"}
              className="bg-primary hover:bg-primary-hover"
            >
              <Edit3 className="w-4 h-4 mr-2" />
              {editMode ? 'Cancel' : 'Edit'}
            </Button>
            {editMode && (
              <Button onClick={handleSave} className="bg-health-excellent hover:bg-health-good">
                <Save className="w-4 h-4 mr-2" />
                Save
              </Button>
            )}
            {onNavigateToDashboard && (
              <Button 
                onClick={onNavigateToDashboard}
                variant="outline"
                className="bg-chart-4 text-white border-0 hover:opacity-90"
              >
                Go to Dashboard
              </Button>
            )}
          </div>
        </div>

        {/* Profile Picture & Basic Info */}
        <Card className="shadow-card">
          <CardHeader className="pb-4">
            <div className="flex items-center space-x-6">
              <div className="relative">
                <Avatar className="w-24 h-24">
                  <AvatarImage src="/placeholder.svg" />
                  <AvatarFallback className="bg-primary text-primary-foreground text-2xl">
                    {profile.name ? profile.name.split(' ').map(n => n[0]).join('') : 'SJ'}
                  </AvatarFallback>
                </Avatar>
                {editMode && (
                  <Button size="icon" className="absolute -bottom-2 -right-2 w-8 h-8 bg-primary hover:bg-primary-hover">
                    <Camera className="w-4 h-4" />
                  </Button>
                )}
              </div>
              <div className="flex-1 space-y-2">
                <h2 className="text-2xl font-semibold text-foreground">
                  {profile.name || 'Your Name'}
                </h2>
                <div className="flex flex-wrap gap-2">
                  <Badge variant="secondary" className="bg-health-excellent/10 text-health-excellent border-health-excellent/20">
                    {profile.age ? `${profile.age} years old` : 'Age not set'}
                  </Badge>
                  <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
                    {profile.gender || 'Gender not specified'}
                  </Badge>
                  {profile.bloodType && (
                    <Badge variant="secondary" className="bg-health-warning/10 text-health-warning border-health-warning/20">
                      Blood Type: {profile.bloodType}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Personal Information */}
        <Card className="shadow-card">
          <CardHeader>
            <CardTitle className="flex items-center text-foreground">
              <User className="w-5 h-5 mr-2 text-primary" />
              Personal Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Full Name</Label>
                <Input
                  id="name"
                  value={profile.name}
                  onChange={(e) => setProfile(prev => ({ ...prev, name: e.target.value }))}
                  onClick={() => !profile.name && handleAutoFill('name')}
                  placeholder="Click to auto-fill or type your name"
                  readOnly={!editMode}
                  className={editMode ? "cursor-pointer hover:border-primary" : "bg-muted"}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="age">Age</Label>
                <Input
                  id="age"
                  type="number"
                  value={profile.age}
                  onChange={(e) => setProfile(prev => ({ ...prev, age: e.target.value }))}
                  onClick={() => !profile.age && handleAutoFill('age')}
                  placeholder="Click to auto-fill age"
                  readOnly={!editMode}
                  className={editMode ? "cursor-pointer hover:border-primary" : "bg-muted"}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="gender">Gender</Label>
                <Select
                  value={profile.gender}
                  onValueChange={(value) => setProfile(prev => ({ ...prev, gender: value }))}
                  disabled={!editMode}
                >
                  <SelectTrigger className={editMode ? "cursor-pointer hover:border-primary" : "bg-muted"}>
                    <SelectValue placeholder="Click to select or auto-fill" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Male">Male</SelectItem>
                    <SelectItem value="Female">Female</SelectItem>
                    <SelectItem value="Other">Other</SelectItem>
                    <SelectItem value="Prefer not to say">Prefer not to say</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="bloodType">Blood Type</Label>
                <Select
                  value={profile.bloodType}
                  onValueChange={(value) => setProfile(prev => ({ ...prev, bloodType: value }))}
                  disabled={!editMode}
                >
                  <SelectTrigger className={editMode ? "cursor-pointer hover:border-primary" : "bg-muted"}>
                    <SelectValue placeholder="Click to select blood type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="A+">A+</SelectItem>
                    <SelectItem value="A-">A-</SelectItem>
                    <SelectItem value="B+">B+</SelectItem>
                    <SelectItem value="B-">B-</SelectItem>
                    <SelectItem value="AB+">AB+</SelectItem>
                    <SelectItem value="AB-">AB-</SelectItem>
                    <SelectItem value="O+">O+</SelectItem>
                    <SelectItem value="O-">O-</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={profile.email}
                  onChange={(e) => setProfile(prev => ({ ...prev, email: e.target.value }))}
                  onClick={() => !profile.email && handleAutoFill('email')}
                  placeholder="Click to auto-fill email"
                  readOnly={!editMode}
                  className={editMode ? "cursor-pointer hover:border-primary" : "bg-muted"}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Phone</Label>
                <Input
                  id="phone"
                  value={profile.phone}
                  onChange={(e) => setProfile(prev => ({ ...prev, phone: e.target.value }))}
                  onClick={() => !profile.phone && handleAutoFill('phone')}
                  placeholder="Click to auto-fill phone"
                  readOnly={!editMode}
                  className={editMode ? "cursor-pointer hover:border-primary" : "bg-muted"}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="emergencyContact">Emergency Contact</Label>
              <Input
                id="emergencyContact"
                value={profile.emergencyContact}
                onChange={(e) => setProfile(prev => ({ ...prev, emergencyContact: e.target.value }))}
                onClick={() => !profile.emergencyContact && handleAutoFill('emergencyContact')}
                placeholder="Click to auto-fill emergency contact"
                readOnly={!editMode}
                className={editMode ? "cursor-pointer hover:border-primary" : "bg-muted"}
              />
            </div>
          </CardContent>
        </Card>

        {/* Physical Information */}
        <Card className="shadow-card">
          <CardHeader>
            <CardTitle className="flex items-center text-foreground">
              <Calendar className="w-5 h-5 mr-2 text-primary" />
              Physical Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="height">Height</Label>
                <Input
                  id="height"
                  value={profile.height}
                  onChange={(e) => setProfile(prev => ({ ...prev, height: e.target.value }))}
                  onClick={() => !profile.height && handleAutoFill('height')}
                  placeholder="Click to auto-fill height (e.g., 5'6&quot;)"
                  readOnly={!editMode}
                  className={editMode ? "cursor-pointer hover:border-primary" : "bg-muted"}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="weight">Weight</Label>
                <Input
                  id="weight"
                  value={profile.weight}
                  onChange={(e) => setProfile(prev => ({ ...prev, weight: e.target.value }))}
                  onClick={() => !profile.weight && handleAutoFill('weight')}
                  placeholder="Click to auto-fill weight (e.g., 165 lbs)"
                  readOnly={!editMode}
                  className={editMode ? "cursor-pointer hover:border-primary" : "bg-muted"}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Medical Conditions */}
        <Card className="shadow-card">
          <CardHeader>
            <CardTitle className="flex items-center text-foreground">
              <Heart className="w-5 h-5 mr-2 text-health-danger" />
              Medical Conditions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-2">
              {profile.conditions.map((condition, index) => (
                <Badge key={index} variant="secondary" className="bg-health-warning/10 text-health-warning border-health-warning/20">
                  {condition}
                  {editMode && (
                    <Button
                      size="icon"
                      variant="ghost"
                      className="ml-2 h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                      onClick={() => removeCondition(condition)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  )}
                </Badge>
              ))}
            </div>
            {editMode && (
              <div>
                <Label>Add Common Conditions (Click to add)</Label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {commonConditions.filter(c => !profile.conditions.includes(c)).map((condition) => (
                    <Badge
                      key={condition}
                      variant="outline"
                      className="cursor-pointer hover:bg-health-warning/10 hover:text-health-warning hover:border-health-warning/20"
                      onClick={() => addCondition(condition)}
                    >
                      <Plus className="w-3 h-3 mr-1" />
                      {condition}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Medications */}
        <Card className="shadow-card">
          <CardHeader>
            <CardTitle className="flex items-center justify-between text-foreground">
              <div className="flex items-center">
                <Pill className="w-5 h-5 mr-2 text-chart-2" />
                Current Medications
              </div>
              {editMode && (
                <Button size="sm" onClick={addMedication} className="bg-primary hover:bg-primary-hover">
                  <Plus className="w-4 h-4 mr-1" />
                  Add Medication
                </Button>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {profile.medications.length === 0 ? (
              <p className="text-muted-foreground italic">No medications listed</p>
            ) : (
              profile.medications.map((med, index) => (
                <div key={index} className="p-4 border rounded-lg bg-card">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label>Medication Name</Label>
                      <Input
                        value={med.name}
                        onChange={(e) => updateMedication(index, 'name', e.target.value)}
                        placeholder="Enter medication name"
                        readOnly={!editMode}
                        className={editMode ? "" : "bg-muted"}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Dosage</Label>
                      <Input
                        value={med.dosage}
                        onChange={(e) => updateMedication(index, 'dosage', e.target.value)}
                        placeholder="e.g., 500mg"
                        readOnly={!editMode}
                        className={editMode ? "" : "bg-muted"}
                      />
                    </div>
                    <div className="space-y-2 relative">
                      <Label>Frequency</Label>
                      <div className="flex">
                        <Input
                          value={med.frequency}
                          onChange={(e) => updateMedication(index, 'frequency', e.target.value)}
                          placeholder="e.g., Twice daily"
                          readOnly={!editMode}
                          className={editMode ? "" : "bg-muted"}
                        />
                        {editMode && (
                          <Button
                            size="icon"
                            variant="ghost"
                            className="ml-2 text-destructive hover:bg-destructive hover:text-destructive-foreground"
                            onClick={() => removeMedication(index)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </CardContent>
        </Card>

        {/* Allergies */}
        <Card className="shadow-card">
          <CardHeader>
            <CardTitle className="flex items-center text-foreground">
              <AlertTriangle className="w-5 h-5 mr-2 text-health-danger" />
              Allergies & Reactions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-2">
              {profile.allergies.map((allergy, index) => (
                <Badge key={index} variant="secondary" className="bg-health-danger/10 text-health-danger border-health-danger/20">
                  {allergy}
                  {editMode && (
                    <Button
                      size="icon"
                      variant="ghost"
                      className="ml-2 h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                      onClick={() => removeAllergy(allergy)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  )}
                </Badge>
              ))}
            </div>
            {editMode && (
              <div>
                <Label>Add Common Allergies (Click to add)</Label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {commonAllergies.filter(a => !profile.allergies.includes(a)).map((allergy) => (
                    <Badge
                      key={allergy}
                      variant="outline"
                      className="cursor-pointer hover:bg-health-danger/10 hover:text-health-danger hover:border-health-danger/20"
                      onClick={() => addAllergy(allergy)}
                    >
                      <Plus className="w-3 h-3 mr-1" />
                      {allergy}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Additional Notes */}
        <Card className="shadow-card">
          <CardHeader>
            <CardTitle className="text-foreground">Additional Notes</CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              value={profile.notes}
              onChange={(e) => setProfile(prev => ({ ...prev, notes: e.target.value }))}
              onClick={() => !profile.notes && handleAutoFill('notes')}
              placeholder="Click to auto-fill or add any additional health information, preferences, or notes..."
              rows={4}
              readOnly={!editMode}
              className={editMode ? "cursor-pointer hover:border-primary" : "bg-muted"}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Profile;