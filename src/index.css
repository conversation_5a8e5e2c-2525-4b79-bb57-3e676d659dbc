@tailwind base;
@tailwind components;
@tailwind utilities;

/* Health-Tech Design System - AI Coach App */

@layer base {
  :root {
    /* Base colors */
    --background: 248 250 252;
    --foreground: 226 29% 15%;

    /* Card system */
    --card: 0 0% 100%;
    --card-foreground: 226 29% 15%;

    /* Health status colors */
    --health-excellent: 158 64% 52%;  /* Green */
    --health-good: 142 71% 45%;       /* Dark green */
    --health-warning: 45 93% 47%;     /* Amber */
    --health-danger: 0 84% 60%;       /* Red */
    --health-critical: 0 72% 51%;     /* Dark red */

    /* Risk level backgrounds */
    --risk-low: 158 100% 96%;
    --risk-medium: 45 100% 96%;
    --risk-high: 0 100% 96%;

    /* Primary health brand */
    --primary: 158 64% 52%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 142 71% 45%;

    /* Secondary system */
    --secondary: 220 14% 96%;
    --secondary-foreground: 226 29% 15%;

    /* Muted system */
    --muted: 220 14% 96%;
    --muted-foreground: 215 20% 65%;

    /* Accent system */
    --accent: 220 14% 96%;
    --accent-foreground: 226 29% 15%;

    /* Destructive system */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    /* Border and input */
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 158 64% 52%;

    /* Gamification colors */
    --badge-bronze: 30 100% 50%;
    --badge-silver: 0 0% 75%;
    --badge-gold: 45 93% 47%;
    --badge-diamond: 224 76% 48%;

    /* Chart colors */
    --chart-1: 158 64% 52%;
    --chart-2: 45 93% 47%;
    --chart-3: 0 84% 60%;
    --chart-4: 224 76% 48%;
    --chart-5: 142 71% 45%;

    /* Gradients */
    --gradient-health: linear-gradient(135deg, hsl(158 64% 52%), hsl(142 71% 45%));
    --gradient-warning: linear-gradient(135deg, hsl(45 93% 47%), hsl(30 100% 50%));
    --gradient-danger: linear-gradient(135deg, hsl(0 84% 60%), hsl(0 72% 51%));
    --gradient-subtle: linear-gradient(180deg, hsl(220 14% 96%), hsl(220 14% 94%));

    /* Shadows */
    --shadow-card: 0 1px 3px 0 hsl(0 0% 0% / 0.1), 0 1px 2px 0 hsl(0 0% 0% / 0.06);
    --shadow-risk: 0 4px 6px -1px hsl(var(--health-danger) / 0.1);
    --shadow-success: 0 4px 6px -1px hsl(var(--health-excellent) / 0.1);

    /* Radius system */
    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Base colors */
    --background: 226 29% 8%;
    --foreground: 220 14% 96%;

    /* Card system */
    --card: 226 29% 10%;
    --card-foreground: 220 14% 96%;

    /* Health status colors (adjusted for dark mode) */
    --health-excellent: 158 64% 45%;
    --health-good: 142 71% 38%;
    --health-warning: 45 93% 40%;
    --health-danger: 0 84% 55%;
    --health-critical: 0 72% 48%;

    /* Risk level backgrounds (dark) */
    --risk-low: 158 40% 8%;
    --risk-medium: 45 40% 8%;
    --risk-high: 0 40% 8%;

    /* Primary health brand */
    --primary: 158 64% 45%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 158 64% 52%;

    /* Secondary system */
    --secondary: 226 29% 15%;
    --secondary-foreground: 220 14% 96%;

    /* Muted system */
    --muted: 226 29% 15%;
    --muted-foreground: 215 20% 65%;

    /* Accent system */
    --accent: 226 29% 15%;
    --accent-foreground: 220 14% 96%;

    /* Destructive system */
    --destructive: 0 84% 55%;
    --destructive-foreground: 0 0% 100%;

    /* Border and input */
    --border: 226 29% 18%;
    --input: 226 29% 18%;
    --ring: 158 64% 45%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}
