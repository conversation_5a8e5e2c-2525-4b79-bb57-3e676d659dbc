import { useState, useEffect } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Login } from "./pages/Login";
import { Signup } from "./pages/Signup";
import Profile from "./pages/Profile";
import { PatientDashboard } from "./pages/PatientDashboard";
import { ClinicianDashboard } from "./pages/ClinicianDashboard";

const queryClient = new QueryClient();

const App = () => {
  const [currentPage, setCurrentPage] = useState('login');
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [userType, setUserType] = useState<'patient' | 'clinician' | null>(null);

  // Handle URL hash changes for navigation
  useEffect(() => {
    const handleHashChange = () => {
      const hash = window.location.hash.slice(1);
      if (hash) {
        setCurrentPage(hash);
      }
    };

    window.addEventListener('hashchange', handleHashChange);
    handleHashChange(); // Check initial hash

    return () => {
      window.removeEventListener('hashchange', handleHashChange);
    };
  }, []);

  const handleLogin = (type: 'patient' | 'clinician', userData: any) => {
    setUserType(type);
    setCurrentUser(userData);
    setCurrentPage('profile');
    window.location.hash = 'profile';
  };

  const handleSignup = (type: 'patient' | 'clinician', userData: any) => {
    setUserType(type);
    setCurrentUser(userData);
    setCurrentPage('profile');
    window.location.hash = 'profile';
  };

  const handleNavigate = (page: string) => {
    if (page === 'login') {
      setCurrentUser(null);
      setUserType(null);
      window.location.hash = '';
    }
    setCurrentPage(page);
    window.location.hash = page;
  };

  const handleNavigateToLogin = () => {
    setCurrentPage('login');
    window.location.hash = '';
  };

  const handleNavigateToSignup = () => {
    setCurrentPage('signup');
    window.location.hash = 'signup';
  };

  const handleNavigateToDashboard = () => {
    const dashboardPage = userType === 'patient' ? 'patient-dashboard' : 'clinician-dashboard';
    setCurrentPage(dashboardPage);
    window.location.hash = dashboardPage;
  };

  const renderPage = () => {
    switch (currentPage) {
      case 'login':
        return <Login onLogin={handleLogin} onNavigateToSignup={handleNavigateToSignup} />;
      case 'signup':
        return <Signup onSignup={handleSignup} onNavigateToLogin={handleNavigateToLogin} />;
      case 'profile':
        return <Profile user={currentUser} userType={userType} onNavigateToDashboard={handleNavigateToDashboard} onNavigate={handleNavigate} />;
      case 'patient-dashboard':
        return <PatientDashboard user={currentUser} onNavigate={handleNavigate} />;
      case 'clinician-dashboard':
        return <ClinicianDashboard user={currentUser} onNavigate={handleNavigate} />;
      default:
        return <Login onLogin={handleLogin} onNavigateToSignup={handleNavigateToSignup} />;
    }
  };

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        {renderPage()}
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
