import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Mi<PERSON>, MicOff, Volume2, X } from "lucide-react";
import { cn } from "@/lib/utils";

interface VoiceAssistantProps {
  className?: string;
}

export function VoiceAssistant({ className }: VoiceAssistantProps) {
  const [isListening, setIsListening] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [transcript, setTranscript] = useState("");
  const [response, setResponse] = useState("");

  const startListening = () => {
    setIsListening(true);
    setIsExpanded(true);
    // Mock transcript for demo
    setTimeout(() => {
      setTranscript("How is my diabetes risk looking today?");
      setIsListening(false);
      
      // Mock AI response
      setTimeout(() => {
        setResponse("Based on your recent glucose readings and activity levels, your diabetes risk is currently at 34% - that's in the medium range. I recommend increasing your water intake and taking a 20-minute walk after meals to help improve your numbers.");
      }, 1000);
    }, 3000);
  };

  const stopListening = () => {
    setIsListening(false);
  };

  const reset = () => {
    setIsExpanded(false);
    setTranscript("");
    setResponse("");
    setIsListening(false);
  };

  if (isExpanded) {
    return (
      <Card className={cn("fixed bottom-4 right-4 w-80 shadow-lg z-50 animate-slide-up", className)}>
        <CardContent className="p-4 space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 rounded-full bg-gradient-health flex items-center justify-center">
                <Volume2 className="h-4 w-4 text-white" />
              </div>
              <span className="font-medium text-sm">Health Assistant</span>
            </div>
            <Button size="icon" variant="ghost" onClick={reset} className="h-6 w-6">
              <X className="h-3 w-3" />
            </Button>
          </div>

          {isListening && (
            <div className="flex items-center gap-2 p-3 bg-risk-low rounded-lg">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-health-excellent rounded-full animate-pulse-health"></div>
                <div className="w-2 h-2 bg-health-excellent rounded-full animate-pulse-health delay-75"></div>
                <div className="w-2 h-2 bg-health-excellent rounded-full animate-pulse-health delay-150"></div>
              </div>
              <p className="text-sm text-health-excellent">Listening...</p>
            </div>
          )}

          {transcript && (
            <div className="space-y-2">
              <Badge variant="outline" className="text-xs">You said:</Badge>
              <p className="text-sm bg-secondary p-3 rounded-lg italic">"{transcript}"</p>
            </div>
          )}

          {response && (
            <div className="space-y-2">
              <Badge variant="outline" className="text-xs bg-gradient-health text-white border-none">AI Response:</Badge>
              <p className="text-sm bg-gradient-subtle p-3 rounded-lg">{response}</p>
            </div>
          )}

          <div className="flex gap-2">
            {!isListening && !response && (
              <Button 
                onClick={startListening} 
                size="sm" 
                className="flex-1 bg-gradient-health hover:bg-primary-hover"
              >
                <Mic className="h-4 w-4 mr-2" />
                Start Recording
              </Button>
            )}
            
            {isListening && (
              <Button 
                onClick={stopListening}
                size="sm" 
                variant="destructive"
                className="flex-1"
              >
                <MicOff className="h-4 w-4 mr-2" />
                Stop Recording
              </Button>
            )}

            {response && (
              <Button 
                onClick={() => {
                  setTranscript("");
                  setResponse("");
                }}
                size="sm"
                variant="outline"
                className="flex-1"
              >
                Ask Another Question
              </Button>
            )}
          </div>

          <p className="text-xs text-muted-foreground text-center">
            Try asking about your health risks, medication reminders, or wellness tips
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Button
      onClick={() => setIsExpanded(true)}
      size="icon"
      className={cn(
        "fixed bottom-4 right-4 h-14 w-14 rounded-full shadow-lg z-50",
        "bg-gradient-health hover:bg-primary-hover",
        "animate-pulse-health",
        className
      )}
    >
      <Mic className="h-6 w-6 text-white" />
    </Button>
  );
}