import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Trophy, Star, Target, Zap, Crown, Award } from "lucide-react";
import { cn } from "@/lib/utils";

interface Achievement {
  id: string;
  title: string;
  description: string;
  type: 'bronze' | 'silver' | 'gold' | 'diamond';
  earned: boolean;
  progress: number;
  maxProgress: number;
  icon: React.ComponentType<any>;
}

const achievements: Achievement[] = [
  {
    id: '1',
    title: 'Step Master',
    description: '10,000 steps in a day',
    type: 'bronze',
    earned: true,
    progress: 10000,
    maxProgress: 10000,
    icon: Target
  },
  {
    id: '2', 
    title: 'Consistency King',
    description: '7 days streak',
    type: 'silver',
    earned: true,
    progress: 7,
    maxProgress: 7,
    icon: Zap
  },
  {
    id: '3',
    title: 'Health Guardian',
    description: '30 days of healthy choices',
    type: 'gold',
    earned: false,
    progress: 23,
    maxProgress: 30,
    icon: Crown
  },
  {
    id: '4',
    title: 'Wellness Warrior',
    description: 'Complete all goals for 60 days',
    type: 'diamond',
    earned: false,
    progress: 12,
    maxProgress: 60,
    icon: Award
  }
];

const badgeConfig = {
  bronze: { color: 'badge-bronze', bg: 'amber-50', label: 'Bronze' },
  silver: { color: 'badge-silver', bg: 'gray-50', label: 'Silver' },
  gold: { color: 'badge-gold', bg: 'yellow-50', label: 'Gold' },
  diamond: { color: 'badge-diamond', bg: 'blue-50', label: 'Diamond' }
};

export function Gamification() {
  const totalPoints = 2847;
  const currentLevel = 7;
  const pointsToNextLevel = 653;
  const earnedBadges = achievements.filter(a => a.earned).length;

  return (
    <div className="space-y-6">
      {/* Points & Level Card */}
      <Card className="shadow-card bg-gradient-health text-white overflow-hidden">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="text-lg font-semibold opacity-95">Level {currentLevel}</h3>
              <p className="text-white/80 text-sm">Health Champion</p>
            </div>
            <div className="text-right">
              <p className="text-2xl font-bold">{totalPoints.toLocaleString()}</p>
              <p className="text-white/80 text-sm">Total Points</p>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-white/80">Progress to Level {currentLevel + 1}</span>
              <span className="text-white/90">{pointsToNextLevel} points to go</span>
            </div>
            <Progress 
              value={75} 
              className="h-2 bg-white/20"
              style={{
                '--progress-foreground': 'white'
              } as any}
            />
          </div>
        </CardContent>
      </Card>

      {/* Achievements */}
      <Card className="shadow-card">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <Trophy className="h-5 w-5 text-badge-gold" />
              Achievements
            </CardTitle>
            <Badge variant="secondary">
              {earnedBadges}/{achievements.length} Earned
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {achievements.map((achievement) => {
            const Icon = achievement.icon;
            const config = badgeConfig[achievement.type];
            const progressPercent = (achievement.progress / achievement.maxProgress) * 100;

            return (
              <div
                key={achievement.id}
                className={cn(
                  "p-4 rounded-lg border transition-all duration-200",
                  achievement.earned 
                    ? "bg-gradient-subtle border-primary/20 shadow-sm" 
                    : "bg-card border-border"
                )}
              >
                <div className="flex items-start gap-3">
                  <div className={cn(
                    "p-3 rounded-full flex-shrink-0 transition-all duration-200",
                    achievement.earned
                      ? `bg-${config.color}/10 animate-badge-celebrate`
                      : "bg-secondary"
                  )}>
                    <Icon className={cn(
                      "h-5 w-5",
                      achievement.earned 
                        ? `text-${config.color}` 
                        : "text-muted-foreground"
                    )} />
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium text-sm">{achievement.title}</h4>
                      <Badge 
                        variant="outline" 
                        className={cn(
                          "text-xs",
                          achievement.earned && `border-${config.color}/30 text-${config.color}`
                        )}
                      >
                        {config.label}
                      </Badge>
                      {achievement.earned && (
                        <Star className="h-4 w-4 text-badge-gold fill-current" />
                      )}
                    </div>
                    
                    <p className="text-xs text-muted-foreground mb-2">
                      {achievement.description}
                    </p>

                    {!achievement.earned && (
                      <div className="space-y-1">
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-muted-foreground">Progress</span>
                          <span className="font-medium">
                            {achievement.progress}/{achievement.maxProgress}
                          </span>
                        </div>
                        <Progress 
                          value={progressPercent}
                          className="h-1.5"
                        />
                      </div>
                    )}

                    {achievement.earned && (
                      <p className="text-xs text-health-excellent flex items-center gap-1">
                        <span className="animate-pulse">✨</span>
                        Achievement unlocked!
                      </p>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </CardContent>
      </Card>
    </div>
  );
}