import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { AlertTriangle, Heart, Activity } from "lucide-react";
import { cn } from "@/lib/utils";

interface RiskCardProps {
  condition: string;
  riskLevel: 'low' | 'medium' | 'high';
  percentage: number;
  trend: 'improving' | 'stable' | 'worsening';
  lastUpdated: string;
}

const riskConfig = {
  low: {
    color: 'health-excellent',
    bg: 'risk-low',
    icon: Heart,
    label: 'Low Risk',
    description: 'Well managed'
  },
  medium: {
    color: 'health-warning', 
    bg: 'risk-medium',
    icon: Activity,
    label: 'Medium Risk',
    description: 'Needs attention'
  },
  high: {
    color: 'health-danger',
    bg: 'risk-high', 
    icon: AlertTriangle,
    label: 'High Risk',
    description: 'Immediate action needed'
  }
};

export function RiskCard({ condition, riskLevel, percentage, trend, lastUpdated }: RiskCardProps) {
  const config = riskConfig[riskLevel];
  const Icon = config.icon;

  return (
    <Card className={cn(
      "relative overflow-hidden shadow-card hover:shadow-lg transition-all duration-200",
      riskLevel === 'high' && "shadow-risk",
      riskLevel === 'low' && "shadow-success"
    )}>
      <div className={cn(
        "absolute inset-0 opacity-5",
        `bg-${config.bg}`
      )} />
      
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">{condition}</CardTitle>
          <div className={cn(
            "p-2 rounded-full",
            `bg-${config.bg}`
          )}>
            <Icon className={cn("h-5 w-5", `text-${config.color}`)} />
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Badge 
              variant={riskLevel === 'high' ? 'destructive' : 'secondary'}
              className={cn(
                riskLevel === 'low' && "bg-risk-low text-health-excellent border-health-excellent/20",
                riskLevel === 'medium' && "bg-risk-medium text-health-warning border-health-warning/20"
              )}
            >
              {config.label}
            </Badge>
            <span className="text-2xl font-bold">{percentage}%</span>
          </div>
          
          <Progress 
            value={percentage} 
            className="h-2"
            // Custom styling based on risk level
            style={{
              '--progress-background': `hsl(var(--${config.bg}))`,
              '--progress-foreground': `hsl(var(--${config.color}))`
            } as any}
          />
        </div>

        <div className="flex items-center justify-between text-sm">
          <span className="text-muted-foreground">{config.description}</span>
          <div className="flex items-center gap-1">
            <div className={cn(
              "w-2 h-2 rounded-full",
              trend === 'improving' && "bg-health-excellent animate-pulse-health",
              trend === 'stable' && "bg-health-warning",
              trend === 'worsening' && "bg-health-danger animate-pulse-health"
            )} />
            <span className="capitalize text-muted-foreground">{trend}</span>
          </div>
        </div>

        <p className="text-xs text-muted-foreground">
          Last updated: {lastUpdated}
        </p>
      </CardContent>
    </Card>
  );
}