import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, TrendingDown, Minus } from "lucide-react";
import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";

ChartJS.register(
  CategoryScale,
  LinearScale, 
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const glucoseData = {
  labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
  datasets: [
    {
      label: 'Blood Glucose (mg/dL)',
      data: [142, 158, 134, 167, 145, 138, 152],
      borderColor: 'hsl(var(--health-warning))',
      backgroundColor: 'hsl(var(--health-warning) / 0.1)',
      tension: 0.4,
      pointBackgroundColor: 'hsl(var(--health-warning))',
      pointBorderColor: 'white',
      pointBorderWidth: 2,
      pointRadius: 6,
    },
  ],
};

const bloodPressureData = {
  labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
  datasets: [
    {
      label: 'Systolic',
      data: [128, 132, 126, 135, 129, 124, 131],
      borderColor: 'hsl(var(--health-excellent))',
      backgroundColor: 'hsl(var(--health-excellent) / 0.1)',
      tension: 0.4,
    },
    {
      label: 'Diastolic',
      data: [82, 85, 79, 88, 81, 78, 84],
      borderColor: 'hsl(var(--health-good))',
      backgroundColor: 'hsl(var(--health-good) / 0.1)',
      tension: 0.4,
    },
  ],
};

const activityData = {
  labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
  datasets: [
    {
      label: 'Daily Steps',
      data: [7823, 9145, 6892, 8756, 9234, 10456, 8932],
      borderColor: 'hsl(var(--primary))',
      backgroundColor: 'hsl(var(--primary) / 0.1)',
      tension: 0.4,
    },
  ],
};

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
      labels: {
        usePointStyle: true,
        padding: 20,
        font: {
          size: 12,
        },
      },
    },
    tooltip: {
      backgroundColor: 'hsl(var(--popover))',
      titleColor: 'hsl(var(--popover-foreground))',
      bodyColor: 'hsl(var(--popover-foreground))',
      borderColor: 'hsl(var(--border))',
      borderWidth: 1,
      cornerRadius: 8,
      displayColors: true,
      usePointStyle: true,
    },
  },
  scales: {
    x: {
      grid: {
        display: false,
      },
      ticks: {
        color: 'hsl(var(--muted-foreground))',
        font: {
          size: 11,
        },
      },
    },
    y: {
      grid: {
        color: 'hsl(var(--border))',
        drawBorder: false,
      },
      ticks: {
        color: 'hsl(var(--muted-foreground))',
        font: {
          size: 11,
        },
      },
    },
  },
  elements: {
    point: {
      hoverRadius: 8,
    },
  },
};

interface TrendMetric {
  label: string;
  current: number;
  previous: number;
  unit: string;
  target?: number;
}

const metrics: TrendMetric[] = [
  {
    label: 'Avg Glucose',
    current: 148,
    previous: 156,
    unit: 'mg/dL',
    target: 140,
  },
  {
    label: 'Blood Pressure',
    current: 129,
    previous: 134,
    unit: 'mmHg',
    target: 120,
  },
  {
    label: 'Daily Steps',
    current: 8748,
    previous: 7892,
    unit: 'steps',
    target: 10000,
  },
  {
    label: 'Sleep Quality',
    current: 7.2,
    previous: 6.8,
    unit: 'hours',
    target: 8,
  },
];

export function HealthTrends() {
  const getTrendIcon = (current: number, previous: number) => {
    if (current > previous) return <TrendingUp className="h-4 w-4 text-health-excellent" />;
    if (current < previous) return <TrendingDown className="h-4 w-4 text-health-danger" />;
    return <Minus className="h-4 w-4 text-muted-foreground" />;
  };

  const getTrendColor = (current: number, previous: number) => {
    if (current > previous) return "text-health-excellent";
    if (current < previous) return "text-health-danger";
    return "text-muted-foreground";
  };

  return (
    <Card className="shadow-card">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold">Health Trends</CardTitle>
      </CardHeader>

      <CardContent>
        {/* Trend Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          {metrics.map((metric) => {
            const change = ((metric.current - metric.previous) / metric.previous * 100);
            const isImproving = metric.current > metric.previous;

            return (
              <div key={metric.label} className="p-3 bg-secondary/50 rounded-lg">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-xs text-muted-foreground">{metric.label}</span>
                  {getTrendIcon(metric.current, metric.previous)}
                </div>
                
                <div className="space-y-1">
                  <p className="text-lg font-semibold">
                    {metric.current.toLocaleString()}{metric.unit}
                  </p>
                  
                  <div className="flex items-center gap-2">
                    <span className={`text-xs ${getTrendColor(metric.current, metric.previous)}`}>
                      {Math.abs(change).toFixed(1)}%
                    </span>
                    
                    {metric.target && (
                      <Badge 
                        variant="outline" 
                        className={`text-xs ${
                          metric.current <= metric.target 
                            ? 'border-health-excellent/30 text-health-excellent' 
                            : 'border-health-warning/30 text-health-warning'
                        }`}
                      >
                        Target: {metric.target}{metric.unit}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Chart Tabs */}
        <Tabs defaultValue="glucose" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="glucose">Blood Glucose</TabsTrigger>
            <TabsTrigger value="bp">Blood Pressure</TabsTrigger>
            <TabsTrigger value="activity">Activity</TabsTrigger>
          </TabsList>

          <TabsContent value="glucose" className="space-y-4">
            <div className="h-64">
              <Line data={glucoseData} options={chartOptions} />
            </div>
            <p className="text-sm text-muted-foreground">
              Track your blood glucose levels throughout the week. Target range: 80-140 mg/dL
            </p>
          </TabsContent>

          <TabsContent value="bp" className="space-y-4">
            <div className="h-64">
              <Line data={bloodPressureData} options={chartOptions} />
            </div>
            <p className="text-sm text-muted-foreground">
              Monitor your blood pressure trends. Target: &lt;120/80 mmHg
            </p>
          </TabsContent>

          <TabsContent value="activity" className="space-y-4">
            <div className="h-64">
              <Line data={activityData} options={chartOptions} />
            </div>
            <p className="text-sm text-muted-foreground">
              Stay active with daily step tracking. Target: 10,000 steps per day
            </p>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}