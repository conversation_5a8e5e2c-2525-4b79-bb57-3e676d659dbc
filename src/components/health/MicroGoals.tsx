import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Footprints, Droplet, Moon, Heart, Plus } from "lucide-react";
import { cn } from "@/lib/utils";

interface MicroGoal {
  id: string;
  type: 'steps' | 'water' | 'sleep' | 'heartrate' | 'custom';
  title: string;
  current: number;
  target: number;
  unit: string;
  streak: number;
  completed: boolean;
}

const goalIcons = {
  steps: Footprints,
  water: Droplet, 
  sleep: Moon,
  heartrate: Heart,
  custom: Plus
};

const mockGoals: MicroGoal[] = [
  {
    id: '1',
    type: 'steps',
    title: 'Daily Steps',
    current: 7823,
    target: 10000,
    unit: 'steps',
    streak: 5,
    completed: false
  },
  {
    id: '2', 
    type: 'water',
    title: 'Water Intake',
    current: 6,
    target: 8,
    unit: 'glasses',
    streak: 12,
    completed: false
  },
  {
    id: '3',
    type: 'sleep',
    title: 'Sleep Duration',
    current: 7.5,
    target: 8,
    unit: 'hours',
    streak: 3,
    completed: false
  },
  {
    id: '4',
    type: 'heartrate',
    title: 'Resting HR',
    current: 68,
    target: 65,
    unit: 'bpm',
    streak: 1,
    completed: true
  }
];

export function MicroGoals() {
  const calculateProgress = (current: number, target: number) => {
    return Math.min((current / target) * 100, 100);
  };

  const getProgressColor = (progress: number, completed: boolean) => {
    if (completed) return 'health-excellent';
    if (progress >= 80) return 'health-good';
    if (progress >= 50) return 'health-warning';
    return 'health-danger';
  };

  return (
    <Card className="shadow-card">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">Daily Micro-Goals</CardTitle>
          <Badge variant="secondary" className="text-xs">
            {mockGoals.filter(g => g.completed).length}/{mockGoals.length} Complete
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {mockGoals.map((goal) => {
          const Icon = goalIcons[goal.type];
          const progress = calculateProgress(goal.current, goal.target);
          const progressColor = getProgressColor(progress, goal.completed);

          return (
            <div
              key={goal.id}
              className={cn(
                "p-4 rounded-lg border transition-all duration-200",
                goal.completed ? "bg-risk-low border-health-excellent/20" : "bg-card border-border"
              )}
            >
              <div className="flex items-center gap-3 mb-3">
                <div className={cn(
                  "p-2 rounded-full",
                  goal.completed ? "bg-health-excellent/10" : "bg-secondary"
                )}>
                  <Icon className={cn(
                    "h-4 w-4",
                    goal.completed ? "text-health-excellent" : "text-muted-foreground"
                  )} />
                </div>
                
                <div className="flex-1">
                  <h4 className="font-medium text-sm">{goal.title}</h4>
                  <p className="text-xs text-muted-foreground">
                    {goal.current} / {goal.target} {goal.unit}
                  </p>
                </div>

                <div className="text-right">
                  <p className="text-sm font-semibold">{Math.round(progress)}%</p>
                  {goal.streak > 0 && (
                    <p className="text-xs text-muted-foreground">
                      🔥 {goal.streak} day{goal.streak > 1 ? 's' : ''}
                    </p>
                  )}
                </div>
              </div>

              <Progress 
                value={progress}
                className="h-2"
                style={{
                  '--progress-foreground': `hsl(var(--${progressColor}))`
                } as any}
              />

              {goal.completed && (
                <div className="mt-2 flex items-center gap-1 text-xs text-health-excellent">
                  <span className="animate-badge-celebrate">🎉</span>
                  <span>Goal completed!</span>
                </div>
              )}
            </div>
          );
        })}

        <Button 
          variant="outline" 
          className="w-full mt-4 border-dashed"
          size="sm"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Custom Goal
        </Button>
      </CardContent>
    </Card>
  );
}